// Exemple d'intégration du composant SvgBezierCurve dans une page existante

'use client';
import styles from '../page.module.scss'
import { useEffect, useState } from 'react'
import { AnimatePresence } from 'framer-motion';
import Preloader from '../../components/Preloader';
import Landing from '../../components/Landing';
import Hero from '../../components/Heros/Hero';
import AnimatedLink from '../../components/AnimatedLink';

import Link from 'next/link';
import Projects from '../../components/Projects';

import Description from '../../components/Description';
import SlidingImages from '../../components/SlidingImages';
import { SvgCurveOnly } from '../../components/SvgBezierCurve';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import Button from '../../common/CubertoButton';

gsap.registerPlugin(ScrollTrigger);

export default function HomeClientWithSvgCurve({ params }) {
  const [isLoading, setIsLoading] = useState(true);
  const locale = params.locale || 'fr';

  return (
    <main className={styles.main}>
      <Hero locale={locale} />
      
      {/* Séparateur animé entre Hero et Projects */}
      <section style={{ 
        height: '200px', 
        backgroundColor: '#f8f9fa',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <SvgCurveOnly 
          strokeColor="#667eea"
          strokeWidth={3}
          height="120px"
          className="w-full max-w-4xl"
        />
      </section>
      
      <Projects isLoading={isLoading} locale={locale} />

      {/* Autre séparateur avec couleur différente */}
      <section style={{ 
        height: '150px', 
        backgroundColor: '#1a1a1a',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <SvgCurveOnly 
          strokeColor="#4ecdc4"
          strokeWidth={2}
          height="100px"
          className="w-full max-w-5xl"
        />
      </section>

      <SlidingImages />
    </main>
  )
}

// Exemple d'utilisation dans une section personnalisée
export function CustomSectionWithCurve() {
  return (
    <section style={{ 
      minHeight: '100vh',
      backgroundColor: '#f8f9fa',
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      padding: '2rem'
    }}>
      <div style={{ textAlign: 'center', marginBottom: '3rem' }}>
        <h2 style={{ fontSize: '3rem', marginBottom: '1rem', color: '#333' }}>
          Section avec courbe interactive
        </h2>
        <p style={{ fontSize: '1.2rem', color: '#666', maxWidth: '600px' }}>
          Survolez la ligne ci-dessous pour voir l'animation de la courbe de Bézier
        </p>
      </div>
      
      <SvgCurveOnly 
        strokeColor="#ff6b6b"
        strokeWidth={4}
        height="150px"
        className="w-full max-w-4xl"
      />
      
      <div style={{ textAlign: 'center', marginTop: '3rem' }}>
        <p style={{ fontSize: '1rem', color: '#888' }}>
          Cette courbe réagit aux mouvements de votre souris
        </p>
      </div>
    </section>
  );
}
