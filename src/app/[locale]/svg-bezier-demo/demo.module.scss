.demoPage {
  min-height: 100vh;
}

.section {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &:nth-child(even) {
    background-color: #f8f9fa;
  }
  
  &:nth-child(odd) {
    background-color: #ffffff;
  }
}

.separator {
  min-height: 50vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  text-align: center;
  
  h2 {
    font-size: 3rem;
    margin-bottom: 1rem;
    font-weight: 700;
  }
  
  p {
    font-size: 1.2rem;
    opacity: 0.9;
  }
}

.container {
  width: 90%;
  max-width: 1200px;
  margin: 0 auto;
  text-align: center;
  
  h3 {
    font-size: 2rem;
    margin-bottom: 2rem;
    color: #333;
    font-weight: 600;
  }
  
  p {
    font-size: 1.1rem;
    color: #666;
    margin-bottom: 2rem;
    line-height: 1.6;
  }
}

.instructions {
  background-color: #1a1a1a;
  color: white;
  padding: 4rem 0;
  
  h2 {
    font-size: 2.5rem;
    margin-bottom: 3rem;
    text-align: center;
    color: white;
  }
}

.codeBlocks {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.codeBlock {
  background-color: #2d2d2d;
  border-radius: 8px;
  padding: 1.5rem;
  text-align: left;
  
  h4 {
    color: #4ecdc4;
    margin-bottom: 1rem;
    font-size: 1.2rem;
  }
  
  pre {
    margin: 0;
    overflow-x: auto;
    
    code {
      color: #e6e6e6;
      font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
      font-size: 0.9rem;
      line-height: 1.5;
      white-space: pre;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .separator {
    h2 {
      font-size: 2rem;
    }
    
    p {
      font-size: 1rem;
    }
  }
  
  .container {
    h3 {
      font-size: 1.5rem;
    }
    
    p {
      font-size: 1rem;
    }
  }
  
  .codeBlocks {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .codeBlock {
    padding: 1rem;
    
    pre code {
      font-size: 0.8rem;
    }
  }
}

@media (max-width: 480px) {
  .separator {
    h2 {
      font-size: 1.5rem;
    }
  }
  
  .container {
    h3 {
      font-size: 1.3rem;
    }
  }
}
