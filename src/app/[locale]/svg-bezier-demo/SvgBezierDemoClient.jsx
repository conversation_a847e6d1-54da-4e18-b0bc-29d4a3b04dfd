"use client";

import SvgBezierCurve, { SvgCurveOnly } from '@/components/SvgBezierCurve';
import { useTranslation } from '@/hooks/useTranslation';
import styles from './demo.module.scss';

export default function SvgBezierDemoClient({ params }) {
  const { t } = useTranslation('common');

  return (
    <main className={styles.demoPage}>
      {/* Version complète */}
      <section className={styles.section}>
        <SvgBezierCurve />
      </section>

      {/* Séparateur avec titre */}
      <section className={styles.separator}>
        <div className={styles.container}>
          <h2>Variantes du composant</h2>
          <p>Différentes configurations possibles</p>
        </div>
      </section>

      {/* Version sans contenu */}
      <section className={styles.section}>
        <div className={styles.container}>
          <h3>Version sans contenu textuel</h3>
          <SvgBezierCurve 
            showContent={false}
            backgroundColor="#ffffff"
          />
        </div>
      </section>

      {/* Version avec couleurs personnalisées */}
      <section className={styles.section}>
        <div className={styles.container}>
          <h3>Version avec couleurs personnalisées</h3>
          <SvgBezierCurve 
            strokeColor="#ff6b6b"
            strokeWidth={3}
            backgroundColor="#1a1a1a"
            showContent={false}
          />
        </div>
      </section>

      {/* Version courbe seule */}
      <section className={styles.section}>
        <div className={styles.container}>
          <h3>Courbe seule (composant SvgCurveOnly)</h3>
          <p>Parfait pour intégrer dans d'autres sections</p>
          <SvgCurveOnly 
            strokeColor="#4ecdc4"
            strokeWidth={2}
            height="80px"
          />
        </div>
      </section>

      {/* Version courbe seule avec hauteur différente */}
      <section className={styles.section}>
        <div className={styles.container}>
          <h3>Courbe avec hauteur personnalisée</h3>
          <SvgCurveOnly 
            strokeColor="#45b7d1"
            strokeWidth={4}
            height="150px"
          />
        </div>
      </section>

      {/* Instructions d'utilisation */}
      <section className={styles.instructions}>
        <div className={styles.container}>
          <h2>Comment utiliser</h2>
          <div className={styles.codeBlocks}>
            <div className={styles.codeBlock}>
              <h4>Version complète</h4>
              <pre>
                <code>{`import SvgBezierCurve from '@/components/SvgBezierCurve';

<SvgBezierCurve 
  strokeColor="#333"
  strokeWidth={2}
  backgroundColor="#f8f9fa"
  showContent={true}
/>`}</code>
              </pre>
            </div>
            
            <div className={styles.codeBlock}>
              <h4>Courbe seule</h4>
              <pre>
                <code>{`import { SvgCurveOnly } from '@/components/SvgBezierCurve';

<SvgCurveOnly 
  strokeColor="#4ecdc4"
  strokeWidth={2}
  height="80px"
/>`}</code>
              </pre>
            </div>
          </div>
        </div>
      </section>
    </main>
  );
}
