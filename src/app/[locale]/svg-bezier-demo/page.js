import SvgBezierDemoClient from './SvgBezierDemoClient';
import { getTranslation } from '@/hooks/useTranslation';

export async function generateMetadata({ params }) {
  const locale = params.locale || 'fr';
  const t = getTranslation(locale, 'common');

  return {
    title: `${t('SvgBezierCurve.title')} - <PERSON><PERSON><PERSON><PERSON>`,
    description: t('SvgBezierCurve.subtitle'),
  };
}

export default function SvgBezierDemoPage({ params }) {
  return <SvgBezierDemoClient params={params} />;
}
