import { NextIntlClientProvider } from 'next-intl';
import { getMessages } from 'next-intl/server';
import SvgBezierCurve from '@/components/SvgBezierCurve';

export const metadata = {
  title: 'SVG Bezier Curve Demo - Kapreon',
  description: 'Démonstration interactive d\'une courbe de Bézier SVG avec animation au survol de la souris.',
};

export default async function SvgBezierDemoPage({ params: { locale } }) {
  const messages = await getMessages();

  return (
    <NextIntlClientProvider messages={messages}>
      <main>
        <SvgBezierCurve />
      </main>
    </NextIntlClientProvider>
  );
}
