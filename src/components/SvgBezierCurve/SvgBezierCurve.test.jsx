// Test simple pour le composant SvgBezierCurve
// Note: Ce fichier nécessiterait Jest et React Testing Library pour fonctionner

import { render, screen, fireEvent } from '@testing-library/react';
import SvgBezierCurve, { SvgCurveOnly } from './index';

// Mock du hook useTranslation
jest.mock('@/hooks/useTranslation', () => ({
  useTranslation: () => ({
    t: (key) => {
      const translations = {
        'SvgBezierCurve.title': 'Smart Development',
        'SvgBezierCurve.subtitle': 'Combining unique design and rich technology...',
        'SvgBezierCurve.areasTitle': 'Areas',
        'SvgBezierCurve.ecommerce': 'E-commerce',
        'SvgBezierCurve.finance': 'Finance',
        'SvgBezierCurve.education': 'Education',
        'SvgBezierCurve.social': 'Social',
        'SvgBezierCurve.entertainment': 'Entertainment',
        'SvgBezierCurve.medicine': 'Medicine',
      };
      return translations[key] || key;
    }
  })
}));

describe('SvgBezierCurve', () => {
  test('renders without crashing', () => {
    render(<SvgBezierCurve />);
    expect(screen.getByText('Smart Development')).toBeInTheDocument();
  });

  test('renders with custom props', () => {
    render(
      <SvgBezierCurve 
        strokeColor="#ff0000"
        strokeWidth={5}
        backgroundColor="#000000"
        showContent={false}
      />
    );
    
    // Vérifie que le contenu n'est pas affiché quand showContent est false
    expect(screen.queryByText('Smart Development')).not.toBeInTheDocument();
  });

  test('SVG path is present', () => {
    render(<SvgBezierCurve />);
    const svgPath = document.querySelector('path');
    expect(svgPath).toBeInTheDocument();
  });

  test('mouse events trigger animations', () => {
    render(<SvgBezierCurve />);
    const interactiveBox = document.querySelector('.box');
    
    // Simule un survol
    fireEvent.mouseEnter(interactiveBox);
    fireEvent.mouseLeave(interactiveBox);
    
    // Le test passerait si aucune erreur n'est levée
    expect(interactiveBox).toBeInTheDocument();
  });
});

describe('SvgCurveOnly', () => {
  test('renders without crashing', () => {
    render(<SvgCurveOnly />);
    const svg = document.querySelector('svg');
    expect(svg).toBeInTheDocument();
  });

  test('applies custom height', () => {
    const { container } = render(<SvgCurveOnly height="200px" />);
    const curveContainer = container.firstChild;
    expect(curveContainer).toHaveStyle('height: 200px');
  });

  test('applies custom stroke properties', () => {
    render(<SvgCurveOnly strokeColor="#ff0000" strokeWidth={5} />);
    const path = document.querySelector('path');
    expect(path).toHaveStyle('stroke: #ff0000');
    expect(path).toHaveStyle('stroke-width: 5');
  });
});

// Tests d'intégration
describe('SvgBezierCurve Integration', () => {
  test('works with different locales', () => {
    // Ce test vérifierait que le composant fonctionne avec différentes langues
    render(<SvgBezierCurve />);
    expect(screen.getByText('Smart Development')).toBeInTheDocument();
  });

  test('responsive behavior', () => {
    // Mock window.innerWidth pour tester le comportement responsive
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 768,
    });

    render(<SvgBezierCurve />);
    
    // Vérifie que le composant se rend correctement sur mobile
    const container = document.querySelector('.container');
    expect(container).toBeInTheDocument();
  });
});

// Tests de performance
describe('SvgBezierCurve Performance', () => {
  test('cleans up event listeners on unmount', () => {
    const { unmount } = render(<SvgBezierCurve />);
    
    // Spy sur removeEventListener
    const removeEventListenerSpy = jest.spyOn(window, 'removeEventListener');
    
    unmount();
    
    // Vérifie que les event listeners sont nettoyés
    expect(removeEventListenerSpy).toHaveBeenCalledWith('resize', expect.any(Function));
    
    removeEventListenerSpy.mockRestore();
  });

  test('cancels animation frames on unmount', () => {
    const { unmount } = render(<SvgBezierCurve />);
    
    // Spy sur cancelAnimationFrame
    const cancelAnimationFrameSpy = jest.spyOn(window, 'cancelAnimationFrame');
    
    unmount();
    
    // Note: Dans un vrai test, on vérifierait que cancelAnimationFrame est appelé
    // si une animation était en cours
    
    cancelAnimationFrameSpy.mockRestore();
  });
});
