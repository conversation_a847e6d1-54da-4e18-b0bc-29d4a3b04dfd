"use client";
import React, { useRef, useEffect } from "react";
import { useTranslation } from "@/hooks/useTranslation";
import styles from "./style.module.scss";

export default function SvgBezierCurve({
  strokeColor = "#333",
  strokeWidth = 2,
  backgroundColor = "#f8f9fa",
  showContent = true
}) {
  const { t } = useTranslation("common");
  const path = useRef(null);
  let progress = 0;
  let x = 0.5;
  let reqId = null;
  let time = Math.PI / 2;

  useEffect(() => {
    setPath(progress);
    const handleResize = () => {
      setPath(progress);
    };
    window.addEventListener('resize', handleResize);
    
    return () => {
      window.removeEventListener('resize', handleResize);
      if (reqId) {
        cancelAnimationFrame(reqId);
      }
    };
  }, []);

  const setPath = (value) => {
    if (!path.current) return;
    const width = window.innerWidth * 0.7;
    path.current.setAttributeNS(null, "d", `M 0 50 Q ${width * x} ${50 + value} ${width} 50`);
  };

  const lerp = (x, y, a) => x * (1 - a) + y * a;

  const animateIn = () => {
    // Si l'animation de sortie est en cours, l'annuler et réinitialiser le temps
    if (reqId) {
      cancelAnimationFrame(reqId);
      time = Math.PI / 2;
    }
    setPath(progress);
    reqId = requestAnimationFrame(animateIn);
  };

  const animateOut = () => {
    let newProgress = progress * Math.sin(time);
    setPath(newProgress);

    progress = lerp(progress, 0, 0.04);
    time += 0.2;

    if (Math.abs(progress) > 0.5) {
      reqId = requestAnimationFrame(animateOut);
    } else {
      // Si la pente est presque plate, on arrête l'animation
      time = Math.PI / 2;
      progress = 0;
    }
  };

  const resetAnimation = () => {
    cancelAnimationFrame(reqId);
    animateOut();
  };

  const manageMouseMove = (e) => {
    const { movementY } = e;
    const box = e.target.getBoundingClientRect();
    x = (e.clientX - box.left) / box.width;
    progress += movementY;
  };

  return (
    <div className={styles.container} style={{ backgroundColor }}>
      <div className={styles.body}>
        <div className={styles.line}>
          <span
            onMouseEnter={animateIn}
            onMouseLeave={resetAnimation}
            onMouseMove={manageMouseMove}
            className={styles.box}
          ></span>
          <svg>
            <path
              ref={path}
              style={{ stroke: strokeColor, strokeWidth }}
            ></path>
          </svg>
        </div>
        {showContent && (
          <>
            <div className={styles.description}>
              <p>{t("SvgBezierCurve.title")}</p>
              <p>{t("SvgBezierCurve.subtitle")}</p>
            </div>
            <div className={styles.tagsContainer}>
              <p>{t("SvgBezierCurve.areasTitle")}</p>
              <div className={styles.tags}>
                <p>{t("SvgBezierCurve.ecommerce")}</p>
                <p>{t("SvgBezierCurve.finance")}</p>
                <p>{t("SvgBezierCurve.education")}</p>
                <p>{t("SvgBezierCurve.social")}</p>
                <p>{t("SvgBezierCurve.entertainment")}</p>
                <p>{t("SvgBezierCurve.medicine")}</p>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
}

// Export du composant simple pour utilisation dans d'autres composants
export { default as SvgCurveOnly } from './SvgCurveOnly';
