"use client";
import React, { useRef, useEffect, useCallback } from "react";
import { useTranslation } from "@/hooks/useTranslation";
import styles from "./style.module.scss";

export default function SvgBezierCurve({
  strokeColor = "#333",
  strokeWidth = 2,
  backgroundColor = "#f8f9fa",
  showContent = true
}) {
  const { t } = useTranslation("common");
  const path = useRef(null);
  const progress = useRef(0);
  const x = useRef(0.5);
  const reqId = useRef(null);
  const time = useRef(Math.PI / 2);
  const isAnimatingOut = useRef(false);

  useEffect(() => {
    setPath(progress.current);
    const handleResize = () => {
      setPath(progress.current);
    };
    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      if (reqId.current) {
        cancelAnimationFrame(reqId.current);
      }
    };
  }, []);

  const setPath = useCallback((value) => {
    if (!path.current) return;

    // Utiliser la largeur du conteneur parent au lieu de la fenêtre
    const container = path.current.closest('.body') || path.current.closest('[class*="body"]');
    const containerWidth = container ? container.offsetWidth : window.innerWidth * 0.7;

    // Ajouter un petit padding pour éviter que les extrémités soient coupées
    const width = containerWidth - 40; // 20px de padding de chaque côté
    const startX = 20; // Commencer à 20px du bord

    path.current.setAttributeNS(null, "d", `M ${startX} 50 Q ${startX + (width * x.current)} ${50 + value} ${startX + width} 50`);
  }, []);

  const lerp = (x, y, a) => x * (1 - a) + y * a;

  const animateIn = useCallback(() => {
    // Si l'animation de sortie est en cours, ignorer l'entrée
    if (isAnimatingOut.current) {
      return;
    }

    // Annuler toute animation en cours
    if (reqId.current) {
      cancelAnimationFrame(reqId.current);
      reqId.current = null;
    }

    // Réinitialiser le temps pour l'animation d'entrée
    time.current = Math.PI / 2;

    // Continuer l'animation d'entrée
    setPath(progress.current);
    reqId.current = requestAnimationFrame(animateIn);
  }, [setPath]);

  const animateOut = useCallback(() => {
    // Marquer qu'on est en train d'animer vers la sortie
    isAnimatingOut.current = true;

    let newProgress = progress.current * Math.sin(time.current);
    setPath(newProgress);

    progress.current = lerp(progress.current, 0, 0.04);
    time.current += 0.2;

    if (Math.abs(progress.current) > 0.5) {
      reqId.current = requestAnimationFrame(animateOut);
    } else {
      // Animation terminée - réinitialiser complètement l'état
      time.current = Math.PI / 2;
      progress.current = 0;
      isAnimatingOut.current = false;
      reqId.current = null;
      setPath(0); // S'assurer que la ligne est droite
    }
  }, [setPath]);

  const resetAnimation = useCallback(() => {
    if (reqId.current) {
      cancelAnimationFrame(reqId.current);
    }
    animateOut();
  }, [animateOut]);

  const handleMouseEnter = useCallback(() => {
    // Ne démarrer l'animation que si on n'est pas en train de sortir
    if (!isAnimatingOut.current) {
      animateIn();
    }
  }, [animateIn]);

  const manageMouseMove = useCallback((e) => {
    // Ignorer les mouvements de souris pendant l'animation de sortie
    if (isAnimatingOut.current) {
      return;
    }

    const { movementY } = e;
    const box = e.target.getBoundingClientRect();
    x.current = (e.clientX - box.left) / box.width;
    progress.current += movementY;
  }, []);

  return (
    <div className={styles.container} style={{ backgroundColor }}>
      <div className={styles.body}>
        <div className={styles.line}>
          <span
            onMouseEnter={handleMouseEnter}
            onMouseLeave={resetAnimation}
            onMouseMove={manageMouseMove}
            className={styles.box}
          ></span>
          <svg>
            <path
              ref={path}
              style={{ stroke: strokeColor, strokeWidth }}
            ></path>
          </svg>
        </div>
        {showContent && (
          <>
            <div className={styles.description}>
              <p>{t("SvgBezierCurve.title")}</p>
              <p>{t("SvgBezierCurve.subtitle")}</p>
            </div>
            <div className={styles.tagsContainer}>
              <p>{t("SvgBezierCurve.areasTitle")}</p>
              <div className={styles.tags}>
                <p>{t("SvgBezierCurve.ecommerce")}</p>
                <p>{t("SvgBezierCurve.finance")}</p>
                <p>{t("SvgBezierCurve.education")}</p>
                <p>{t("SvgBezierCurve.social")}</p>
                <p>{t("SvgBezierCurve.entertainment")}</p>
                <p>{t("SvgBezierCurve.medicine")}</p>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
}

// Export du composant simple pour utilisation dans d'autres composants
export { default as SvgCurveOnly } from './SvgCurveOnly';
