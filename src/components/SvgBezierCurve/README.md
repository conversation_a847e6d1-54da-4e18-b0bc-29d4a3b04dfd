# SVG Bezier Curve Component

Un composant React interactif qui affiche une courbe de Bézier SVG animée au survol de la souris. Basé sur le tutoriel d'<PERSON>.

## Fonctionnalités

- **Animation interactive** : La courbe se déforme en suivant les mouvements de la souris
- **Animation d'entrée et de sortie** : Utilise la fonction sinus et l'interpolation linéaire pour des transitions fluides
- **Responsive** : S'adapte aux différentes tailles d'écran
- **Configurable** : Props pour personnaliser l'apparence
- **Internationalisé** : Support des traductions français/anglais

## Utilisation

### Utilisation basique

```jsx
import SvgBezierCurve from '@/components/SvgBezierCurve';

export default function MyPage() {
  return (
    <div>
      <SvgBezierCurve />
    </div>
  );
}
```

### Utilisation avec props personnalisées

```jsx
import SvgBezierCurve from '@/components/SvgBezierCurve';

export default function MyPage() {
  return (
    <div>
      <SvgBezierCurve 
        strokeColor="#ff6b6b"
        strokeWidth={3}
        backgroundColor="#1a1a1a"
        showContent={false}
      />
    </div>
  );
}
```

## Props

| Prop | Type | Défaut | Description |
|------|------|--------|-------------|
| `strokeColor` | `string` | `"#333"` | Couleur de la ligne SVG |
| `strokeWidth` | `number` | `2` | Épaisseur de la ligne SVG |
| `backgroundColor` | `string` | `"#f8f9fa"` | Couleur de fond du conteneur |
| `showContent` | `boolean` | `true` | Afficher ou masquer le contenu textuel |

## Fonctionnement technique

### Courbe de Bézier quadratique

Le composant utilise une courbe de Bézier quadratique SVG avec la syntaxe :
```
M 0 50 Q ${width * x} ${50 + value} ${width} 50
```

- `M 0 50` : Point de départ au milieu gauche
- `Q x1 y1 x2 y2` : Courbe quadratique avec point de contrôle et point final
- `${width * x}` : Position horizontale du point de contrôle (basée sur la position de la souris)
- `${50 + value}` : Position verticale du point de contrôle (basée sur le mouvement de la souris)

### Animations

1. **Animation d'entrée** (`animateIn`) : 
   - Se déclenche au survol (`onMouseEnter`)
   - Met à jour la courbe en temps réel avec `requestAnimationFrame`

2. **Animation de sortie** (`animateOut`) :
   - Se déclenche quand la souris quitte la zone (`onMouseLeave`)
   - Utilise la fonction sinus pour créer un effet d'oscillation
   - Utilise l'interpolation linéaire (lerp) pour revenir progressivement à l'état initial

### Gestion des événements souris

- `onMouseMove` : Capture les mouvements pour ajuster la position et l'intensité de la courbe
- `movementY` : Utilisé pour l'intensité de la déformation
- Position relative de la souris : Utilisée pour la position horizontale du point de contrôle

## Traductions

Le composant utilise le système de traduction personnalisé du projet. Les clés de traduction sont définies dans :

- `locales/fr/common.json`
- `locales/en/common.json`

Sous la section `SvgBezierCurve`.

## Styles

Les styles sont définis dans `style.module.scss` et incluent :

- Layout responsive
- Styles pour la zone interactive
- Animations CSS pour les tags
- Support mobile

## Exemples d'utilisation avancés

### Intégration comme séparateur de section

```jsx
import { SvgCurveOnly } from '@/components/SvgBezierCurve';

export default function MyPage() {
  return (
    <div>
      <section>Contenu de la première section</section>

      {/* Séparateur animé */}
      <div style={{ height: '150px', backgroundColor: '#f8f9fa' }}>
        <SvgCurveOnly
          strokeColor="#667eea"
          strokeWidth={3}
          height="120px"
        />
      </div>

      <section>Contenu de la deuxième section</section>
    </div>
  );
}
```

### Utilisation avec Framer Motion

```jsx
import { motion } from 'framer-motion';
import { SvgCurveOnly } from '@/components/SvgBezierCurve';

export default function AnimatedSection() {
  return (
    <motion.div
      initial={{ opacity: 0 }}
      whileInView={{ opacity: 1 }}
      transition={{ duration: 0.8 }}
    >
      <SvgCurveOnly strokeColor="#4ecdc4" height="100px" />
    </motion.div>
  );
}
```

## Performance

- **Optimisé pour les animations** : Utilise `requestAnimationFrame` pour des animations fluides
- **Nettoyage automatique** : Les event listeners et animations sont nettoyés au démontage
- **Responsive** : S'adapte automatiquement à la taille de l'écran
- **Léger** : Aucune dépendance externe lourde

## Accessibilité

Le composant est conçu pour être accessible :
- La zone interactive est suffisamment grande pour les interactions tactiles
- Les animations respectent les préférences utilisateur (peut être étendu avec `prefers-reduced-motion`)
- Le contraste des couleurs peut être personnalisé

## Tests

Des tests sont disponibles dans `SvgBezierCurve.test.jsx` couvrant :
- Rendu de base
- Props personnalisées
- Événements souris
- Nettoyage des ressources
- Comportement responsive

Pour exécuter les tests :
```bash
npm test SvgBezierCurve.test.jsx
```

## Dépannage

### La courbe ne s'affiche pas
- Vérifiez que le conteneur parent a une largeur définie
- Assurez-vous que `window` est disponible (composant côté client uniquement)

### Les animations sont saccadées
- Vérifiez les performances du navigateur
- Réduisez la fréquence des mises à jour si nécessaire

### Problèmes de responsive
- Le composant utilise la largeur du conteneur parent avec un padding de 40px
- Les extrémités de la courbe sont maintenant visibles grâce au padding

### Comportement bizarre au re-survol
- **Problème résolu** : Les variables d'état utilisent maintenant `useRef` au lieu de variables locales
- Cela évite la réinitialisation des valeurs à chaque rendu

### Bout droit de la courbe coupé
- **Problème résolu** : La courbe utilise maintenant un padding de 20px de chaque côté
- Le SVG a `overflow: visible` pour permettre aux courbes de déborder légèrement
- La largeur est calculée en fonction du conteneur parent plutôt que de la fenêtre

### Corrections apportées (v1.2)

1. **Variables d'état persistantes** :
   ```jsx
   // Avant (problématique)
   let progress = 0;
   let x = 0.5;

   // Après (corrigé)
   const progress = useRef(0);
   const x = useRef(0.5);
   ```

2. **Calcul de largeur amélioré** :
   ```jsx
   // Avant
   const width = window.innerWidth * 0.7;
   path.current.setAttributeNS(null, "d", `M 0 50 Q ${width * x} ${50 + value} ${width} 50`);

   // Après
   const containerWidth = container ? container.offsetWidth : window.innerWidth * 0.7;
   const width = containerWidth - 40; // Padding
   const startX = 20;
   path.current.setAttributeNS(null, "d", `M ${startX} 50 Q ${startX + (width * x.current)} ${50 + value} ${startX + width} 50`);
   ```

3. **Animation non-interruptible** :
   ```jsx
   // Nouveau comportement : l'animation de sortie ne peut pas être interrompue
   const handleMouseEnter = useCallback(() => {
     if (!isAnimatingOut.current) {
       animateIn();
     }
   }, [animateIn]);

   const manageMouseMove = useCallback((e) => {
     if (isAnimatingOut.current) {
       return; // Ignorer les mouvements pendant l'animation de sortie
     }
     // ... gestion normale
   }, []);
   ```

4. **Styles CSS améliorés** :
   - Ajout de `overflow: visible` sur les conteneurs SVG
   - `vector-effect: non-scaling-stroke` pour maintenir l'épaisseur de trait constante
   - Indicateur visuel pour l'état "verrouillé"

## Roadmap

Améliorations futures possibles :
- [ ] Support des courbes de Bézier cubiques
- [ ] Animations basées sur le scroll
- [ ] Présets de couleurs thématiques
- [ ] Support des animations CSS au lieu de JavaScript
- [ ] Mode sombre automatique

## Inspiration

Ce composant est basé sur le tutoriel ["SVG Bezier Curve"](https://blog.olivierlarose.com/tutorials/svg-bezier-curve) d'Olivier Larose, adapté pour s'intégrer dans l'architecture du projet Kapreon.
