"use client";
import React, { useRef, useEffect, useCallback } from "react";
import styles from "./style.module.scss";

export default function SvgCurveOnly({
  strokeColor = "#333",
  strokeWidth = 2,
  height = "100px",
  className = ""
}) {
  const path = useRef(null);
  const progress = useRef(0);
  const x = useRef(0.5);
  const reqId = useRef(null);
  const time = useRef(Math.PI / 2);

  useEffect(() => {
    setPath(progress.current);
    const handleResize = () => {
      setPath(progress.current);
    };
    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      if (reqId.current) {
        cancelAnimationFrame(reqId.current);
      }
    };
  }, []);

  const setPath = useCallback((value) => {
    if (!path.current) return;

    // Utiliser la largeur du conteneur parent
    const container = path.current.closest('[class*="curveOnly"]') || path.current.parentElement;
    const containerWidth = container ? container.offsetWidth : window.innerWidth * 0.7;

    // Ajouter un padding pour éviter que les extrémités soient coupées
    const width = containerWidth - 40;
    const startX = 20;
    const midPoint = parseInt(height) / 2;

    path.current.setAttributeNS(null, "d", `M ${startX} ${midPoint} Q ${startX + (width * x.current)} ${midPoint + value} ${startX + width} ${midPoint}`);
  }, [height]);

  const lerp = (x, y, a) => x * (1 - a) + y * a;

  const animateIn = useCallback(() => {
    if (reqId.current) {
      cancelAnimationFrame(reqId.current);
      time.current = Math.PI / 2;
    }
    setPath(progress.current);
    reqId.current = requestAnimationFrame(animateIn);
  }, [setPath]);

  const animateOut = useCallback(() => {
    let newProgress = progress.current * Math.sin(time.current);
    setPath(newProgress);

    progress.current = lerp(progress.current, 0, 0.04);
    time.current += 0.2;

    if (Math.abs(progress.current) > 0.5) {
      reqId.current = requestAnimationFrame(animateOut);
    } else {
      time.current = Math.PI / 2;
      progress.current = 0;
    }
  }, [setPath]);

  const resetAnimation = useCallback(() => {
    if (reqId.current) {
      cancelAnimationFrame(reqId.current);
    }
    animateOut();
  }, [animateOut]);

  const manageMouseMove = useCallback((e) => {
    const { movementY } = e;
    const box = e.target.getBoundingClientRect();
    x.current = (e.clientX - box.left) / box.width;
    progress.current += movementY;
  }, []);

  return (
    <div className={`${styles.curveOnly} ${className}`} style={{ height }}>
      <span 
        onMouseEnter={animateIn}
        onMouseLeave={resetAnimation}
        onMouseMove={manageMouseMove}
        className={styles.curveBox}
        style={{ height: height === "100px" ? "20px" : "20%" }}
      ></span>
      <svg style={{ width: "100%", height: "100%" }}>
        <path 
          ref={path} 
          style={{ stroke: strokeColor, strokeWidth, fill: "none" }}
        ></path>
      </svg>
    </div>
  );
}
