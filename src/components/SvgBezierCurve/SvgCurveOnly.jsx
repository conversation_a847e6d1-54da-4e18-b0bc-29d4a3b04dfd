"use client";
import React, { useRef, useEffect } from "react";
import styles from "./style.module.scss";

export default function SvgCurveOnly({ 
  strokeColor = "#333", 
  strokeWidth = 2,
  height = "100px",
  className = ""
}) {
  const path = useRef(null);
  let progress = 0;
  let x = 0.5;
  let reqId = null;
  let time = Math.PI / 2;

  useEffect(() => {
    setPath(progress);
    const handleResize = () => {
      setPath(progress);
    };
    window.addEventListener('resize', handleResize);
    
    return () => {
      window.removeEventListener('resize', handleResize);
      if (reqId) {
        cancelAnimationFrame(reqId);
      }
    };
  }, []);

  const setPath = (value) => {
    if (!path.current) return;
    const width = window.innerWidth * 0.7;
    const midPoint = parseInt(height) / 2;
    path.current.setAttributeNS(null, "d", `M 0 ${midPoint} Q ${width * x} ${midPoint + value} ${width} ${midPoint}`);
  };

  const lerp = (x, y, a) => x * (1 - a) + y * a;

  const animateIn = () => {
    if (reqId) {
      cancelAnimationFrame(reqId);
      time = Math.PI / 2;
    }
    setPath(progress);
    reqId = requestAnimationFrame(animateIn);
  };

  const animateOut = () => {
    let newProgress = progress * Math.sin(time);
    setPath(newProgress);

    progress = lerp(progress, 0, 0.04);
    time += 0.2;

    if (Math.abs(progress) > 0.5) {
      reqId = requestAnimationFrame(animateOut);
    } else {
      time = Math.PI / 2;
      progress = 0;
    }
  };

  const resetAnimation = () => {
    cancelAnimationFrame(reqId);
    animateOut();
  };

  const manageMouseMove = (e) => {
    const { movementY } = e;
    const box = e.target.getBoundingClientRect();
    x = (e.clientX - box.left) / box.width;
    progress += movementY;
  };

  return (
    <div className={`${styles.curveOnly} ${className}`} style={{ height }}>
      <span 
        onMouseEnter={animateIn}
        onMouseLeave={resetAnimation}
        onMouseMove={manageMouseMove}
        className={styles.curveBox}
        style={{ height: height === "100px" ? "20px" : "20%" }}
      ></span>
      <svg style={{ width: "100%", height: "100%" }}>
        <path 
          ref={path} 
          style={{ stroke: strokeColor, strokeWidth, fill: "none" }}
        ></path>
      </svg>
    </div>
  );
}
