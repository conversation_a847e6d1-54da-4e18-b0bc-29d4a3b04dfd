.container {
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
}

.body {
  width: 70%;
  max-width: 1200px;
  position: relative;
}

.line {
  position: relative;
  height: 100px;
  margin-bottom: 3rem;
  width: 100%;
  overflow: visible; // Permet au SVG de déborder si nécessaire

  .box {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 20px;
    background-color: transparent;
    cursor: pointer;
    z-index: 2;
    transition: height 0.3s ease;

    &:hover {
      height: 100px;
    }
  }

  svg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    overflow: visible; // Permet aux courbes de déborder légèrement

    path {
      stroke: #333;
      stroke-width: 2;
      fill: none;
      vector-effect: non-scaling-stroke; // Garde l'épaisseur constante
    }
  }
}

.description {
  margin-bottom: 3rem;
  
  p:first-child {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: #333;
    line-height: 1.2;
  }
  
  p:last-child {
    font-size: 1.1rem;
    line-height: 1.6;
    color: #666;
    max-width: 600px;
  }
}

.tagsContainer {
  p:first-child {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #333;
  }
}

.tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  
  p {
    background-color: #e9ecef;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    color: #495057;
    margin: 0;
    transition: all 0.3s ease;
    
    &:hover {
      background-color: #333;
      color: white;
      transform: translateY(-2px);
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .body {
    width: 90%;
  }
  
  .description {
    p:first-child {
      font-size: 2rem;
    }
    
    p:last-child {
      font-size: 1rem;
    }
  }
  
  .tags {
    gap: 0.5rem;
    
    p {
      font-size: 0.8rem;
      padding: 0.4rem 0.8rem;
    }
  }
}

@media (max-width: 480px) {
  .description {
    p:first-child {
      font-size: 1.5rem;
    }
  }

  .line {
    height: 80px;

    .box:hover {
      height: 80px;
    }
  }
}

// Styles pour le composant SvgCurveOnly
.curveOnly {
  position: relative;
  width: 100%;
  overflow: visible;

  .curveBox {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    background-color: transparent;
    cursor: pointer;
    z-index: 2;
    transition: height 0.3s ease;

    &:hover {
      height: 100% !important;
    }
  }

  svg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    overflow: visible;

    path {
      vector-effect: non-scaling-stroke;
    }
  }
}
